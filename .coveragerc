[run]
source = .
parallel = true
omit =
    # Exclude test files
    tests/*
    */tests/*
    # Exclude virtual environment
    .venv/*
    venv/*
    env/*
    # Exclude setup files
    setup.py
    # Exclude utility scripts that are not core functionality
    fix_*.py
    setup_*.py
    # Exclude examples and CLI
    */examples/*
    */cli/*
    # Exclude third-party packages
    */site-packages/*
    # Exclude specific directories temporarily - reduced exclusions to improve coverage
    test_*.py
    verify_*.py
    # Exclude large modules without tests to focus coverage on tested modules
    marketing/*
    monetization/*
    niche_analysis/*
    collaboration/*
    ai_models/benchmarking/*
    ai_models/caching/*
    ai_models/fine_tuning/*
    ai_models/metrics/*
    ai_models/optimization/*
    ai_models/serving/*
    common_utils/secrets/*
    common_utils/logging/alert_system.py
    common_utils/logging/centralized_logging.py
    common_utils/logging/dashboard_auth.py
    common_utils/logging/ml_log_analysis.py
    dev_tools/*
    scripts/*
    migrations/*
    artist_experiments/*
    # Keep these core modules for coverage
    # ui/*
    # services/* - Temporarily include services for better coverage
branch = True

[report]
exclude_lines =
    # Skip any line with a pragma
    pragma: no cover
    # Skip representation methods
    def __repr__
    # Skip defensive assertion code
    raise NotImplementedError
    raise ImportError
    if __name__ == .__main__.:
    if self.debug:
    # Skip abstract methods
    @abstractmethod
    # Skip pass statements
    pass
    # Skip type checking blocks
    if TYPE_CHECKING:
    # Skip exception handling that can't be tested easily
    except ImportError:
fail_under = 15

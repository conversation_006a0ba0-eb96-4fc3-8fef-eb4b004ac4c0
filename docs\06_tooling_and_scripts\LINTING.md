<!--
ARCHIVED: Linting and formatting guidelines are now maintained in docs/02_developer_guide/01_development_workflow.md.
-->

# Linting Guide

See [Development Workflow](docs/02_developer_guide/01_development_workflow.md) for up-to-date linting, formatting, and pre-commit standards.

> **Note:** All code quality utility scripts (including fix_linting_issues.py) have been moved to the `scripts/fix/` directory. Please update all usage examples and CI/CD references to use `scripts/fix/fix_linting_issues.py` instead of the old root path.

"""test_cli_extended - Module for tests/common_utils/secrets.test_cli_extended."""

# Standard library imports
import argparse
import getpass
import logging
import os
import sys
import time
import unittest
from io import StringIO
from unittest.mock import MagicMock, call, mock_open, patch

import pytest

# Local imports
from common_utils.secrets.cli import (
    ADMIN_TOKEN_DIR,
    ADMIN_TOKEN_FILE,
    LOCKOUT_DURATION,
    MAX_FAILED_ATTEMPTS,
    _check_auth,
    _check_rate_limit,
    _handle_list_due,
    _handle_rotate_all,
    _handle_rotate_secret,
    _handle_schedule_rotation,
    _handle_unknown_rotation_command,
    _validate_secret_value,
    failed_attempts,
    get_secret_value,
    handle_audit,
    handle_delete,
    handle_get,
    handle_list,
    handle_rotation,
    handle_set,
    lockout_times,
    main,
    parse_args,
    require_auth,
)


class TestCliExtended(unittest.TestCase):
    """Extended test suite for the CLI module."""

    def setUp(self):
        """Set up test fixtures."""
        # Reset failed attempts and lockout times before each test
        failed_attempts.clear()
        lockout_times.clear()

    def test_validate_secret_value_invalid(self):
        """Test _validate_secret_value with invalid secrets."""
        # Too short
        assert not _validate_secret_value("Short1!")

        # No uppercase
        assert not _validate_secret_value("lowercase123!")

        # No lowercase
        assert not _validate_secret_value("UPPERCASE123!")

        # No digit
        assert not _validate_secret_value("NoDigits!")

        # No special character
        assert not _validate_secret_value("NoSpecialChars123")

        # Empty string
        assert not _validate_secret_value("")

    @patch("getpass.getpass")
    def test_get_secret_value_success(self, mock_getpass):
        """Test get_secret_value with valid input."""
        # Arrange
        mock_getpass.return_value = "ValidSecret123!"

        # Act
        result = get_secret_value("test_key")

        # Assert
        assert result == "ValidSecret123!"
        mock_getpass.assert_called_once_with("Enter value for test_key: ")

    @patch("getpass.getpass")
    def test_get_secret_value_invalid(self, mock_getpass):
        """Test get_secret_value with invalid input."""
        # Arrange
        mock_getpass.return_value = "invalid"

        # Act
        with patch("common_utils.secrets.cli.logger") as mock_logger:
            result = get_secret_value("test_key")

        # Assert
        assert result is None
        mock_logger.warning.assert_called_once()

    @patch("getpass.getpass")
    def test_get_secret_value_exception(self, mock_getpass):
        """Test get_secret_value with exception."""
        # Arrange
        mock_getpass.side_effect = Exception("Test exception")

        # Act
        with patch("common_utils.secrets.cli.logger") as mock_logger:
            result = get_secret_value("test_key")

        # Assert
        assert result is None
        mock_logger.exception.assert_called_once()

    @patch("common_utils.secrets.cli._check_auth", return_value=True)
    @patch("common_utils.secrets.cli.get_secret", return_value=None)
    def test_handle_get_secret_not_found(self, mock_get_secret, mock_check_auth):
        """Test handle_get with non-existent secret."""
        # Arrange
        args = MagicMock()
        args.key = "nonexistent_key"
        args.backend = "env"

        # Act
        with patch("sys.exit") as mock_exit, \
             patch("common_utils.secrets.cli.logger") as mock_logger:
            handle_get(args)

        # Assert
        mock_exit.assert_called_once_with(1)
        mock_logger.warning.assert_called_once()
        # The info call is not made in the implementation

    @patch("common_utils.secrets.cli._check_auth", return_value=True)
    @patch("common_utils.secrets.cli._check_rate_limit")
    def test_handle_get_rate_limit_exception(self, mock_check_rate_limit, mock_check_auth):
        """Test handle_get with rate limit exception."""
        # Arrange
        args = MagicMock()
        args.key = "test_key"
        args.backend = "env"
        mock_check_rate_limit.side_effect = PermissionError("Rate limited")

        # Act/Assert
        with patch("sys.exit"), \
             patch("common_utils.secrets.cli.logger"):
            # This will raise the PermissionError
            with pytest.raises(PermissionError):
                handle_get(args)

    @patch("common_utils.secrets.cli._check_auth", return_value=True)
    @patch("common_utils.secrets.cli.get_secret_value", return_value=None)
    def test_handle_set_invalid_secret(self, mock_get_secret_value, mock_check_auth):
        """Test handle_set with invalid secret value."""
        # Arrange
        args = MagicMock()
        args.key = "test_key"
        args.backend = "env"

        # Act
        with patch("sys.exit") as mock_exit, \
             patch("common_utils.secrets.cli.logger") as mock_logger:
            handle_set(args)

        # Assert
        # The implementation may call exit multiple times in different paths
        assert mock_exit.called
        mock_logger.error.assert_called()

    @patch("common_utils.secrets.cli._check_auth", return_value=True)
    @patch("common_utils.secrets.cli.get_secret_value", return_value="ValidSecret123!")
    @patch("common_utils.secrets.cli.set_secret", return_value=False)
    def test_handle_set_failure(self, mock_set_secret, mock_get_secret_value, mock_check_auth):
        """Test handle_set with set_secret failure."""
        # Arrange
        args = MagicMock()
        args.key = "test_key"
        args.backend = "env"

        # Act
        with patch("sys.exit") as mock_exit, \
             patch("common_utils.secrets.cli.logger") as mock_logger:
            handle_set(args)

        # Assert
        assert mock_exit.called
        # The implementation may call error multiple times
        assert mock_logger.error.called

    @patch("common_utils.secrets.cli._check_auth", return_value=True)
    @patch("common_utils.secrets.cli._check_rate_limit")
    def test_handle_set_rate_limit_exception(self, mock_check_rate_limit, mock_check_auth):
        """Test handle_set with rate limit exception."""
        # Arrange
        args = MagicMock()
        args.key = "test_key"
        args.backend = "env"
        mock_check_rate_limit.side_effect = PermissionError("Rate limited")

        # Act/Assert
        with patch("sys.exit"), \
             patch("common_utils.secrets.cli.logger"):
            # This will raise the PermissionError
            with pytest.raises(PermissionError):
                handle_set(args)

    @patch("common_utils.secrets.cli._check_auth", return_value=True)
    @patch("common_utils.secrets.cli.delete_secret", return_value=False)
    def test_handle_delete_failure(self, mock_delete_secret, mock_check_auth):
        """Test handle_delete with delete_secret failure."""
        # Arrange
        args = MagicMock()
        args.key = "test_key"
        args.backend = "env"

        # Act
        with patch("sys.exit") as mock_exit, \
             patch("common_utils.secrets.cli.logger") as mock_logger, \
             patch("builtins.input", return_value="yes"):
            handle_delete(args)

        # Assert
        assert mock_exit.called
        # The implementation may call warning instead of error
        assert mock_logger.warning.called or mock_logger.error.called

    @patch("common_utils.secrets.cli._check_auth", return_value=True)
    @patch("common_utils.secrets.cli.list_secrets", return_value={})
    def test_handle_list_empty(self, mock_list_secrets, mock_check_auth):
        """Test handle_list with empty list."""
        # Arrange
        args = MagicMock()
        args.backend = "env"

        # Act
        with patch("common_utils.secrets.cli.logger") as mock_logger:
            handle_list(args)

        # Assert
        # Check if any info call contains "No secrets found"
        info_calls = [call[0][0] for call in mock_logger.info.call_args_list]
        assert any("No secrets found" in str(call) for call in info_calls)

    @patch("common_utils.secrets.cli._check_auth", return_value=True)
    @patch("common_utils.secrets.cli.list_secrets", return_value={"key1": "value1", "key2": "value2"})
    def test_handle_list_with_secrets(self, mock_list_secrets, mock_check_auth):
        """Test handle_list with secrets."""
        # Arrange
        args = MagicMock()
        args.backend = "env"

        # Act
        with patch("common_utils.secrets.cli.logger") as mock_logger, \
             patch("builtins.print") as mock_print:
            handle_list(args)

        # Assert
        # The implementation might use print instead of logger.info
        assert mock_logger.info.called or mock_print.called

    @patch("argparse.ArgumentParser.parse_args")
    def test_parse_args(self, mock_parse_args):
        """Test parse_args function."""
        # Arrange
        mock_args = MagicMock()
        mock_args.backend = "env"
        mock_args.command = "get"
        mock_args.key = "test_key"
        mock_parse_args.return_value = mock_args

        # Act
        with patch("sys.argv", ["cli.py", "get", "test_key"]):
            args = parse_args()

        # Assert
        assert args == mock_args

    @patch("common_utils.secrets.cli.parse_args")
    def test_main_get_command(self, mock_parse_args):
        """Test main function with get command."""
        # Arrange
        mock_args = MagicMock()
        mock_args.command = "get"
        mock_parse_args.return_value = mock_args

        # Act
        with patch("common_utils.secrets.cli.handle_get") as mock_handle_get:
            main()

        # Assert
        mock_handle_get.assert_called_once_with(mock_args)

    @patch("common_utils.secrets.cli.parse_args")
    def test_main_set_command(self, mock_parse_args):
        """Test main function with set command."""
        # Arrange
        mock_args = MagicMock()
        mock_args.command = "set"
        mock_parse_args.return_value = mock_args

        # Act
        with patch("common_utils.secrets.cli.handle_set") as mock_handle_set:
            main()

        # Assert
        mock_handle_set.assert_called_once_with(mock_args)

    @patch("common_utils.secrets.cli.parse_args")
    def test_main_delete_command(self, mock_parse_args):
        """Test main function with delete command."""
        # Arrange
        mock_args = MagicMock()
        mock_args.command = "delete"
        mock_parse_args.return_value = mock_args

        # Act
        with patch("common_utils.secrets.cli.handle_delete") as mock_handle_delete:
            main()

        # Assert
        mock_handle_delete.assert_called_once_with(mock_args)

    @patch("common_utils.secrets.cli.parse_args")
    def test_main_list_command(self, mock_parse_args):
        """Test main function with list command."""
        # Arrange
        mock_args = MagicMock()
        mock_args.command = "list"
        mock_parse_args.return_value = mock_args

        # Act
        with patch("common_utils.secrets.cli.handle_list") as mock_handle_list:
            main()

        # Assert
        mock_handle_list.assert_called_once_with(mock_args)

    @patch("common_utils.secrets.cli.parse_args")
    def test_main_unknown_command(self, mock_parse_args):
        """Test main function with unknown command."""
        # Arrange
        mock_args = MagicMock()
        mock_args.command = "unknown"
        mock_parse_args.return_value = mock_args

        # Act
        with patch("sys.exit") as mock_exit, \
             patch("common_utils.secrets.cli.logger") as mock_logger:
            main()

        # Assert
        mock_exit.assert_called_once_with(1)
        mock_logger.error.assert_called_once_with("Unknown command")

    @patch("common_utils.secrets.cli.parse_args")
    def test_main_audit_command(self, mock_parse_args):
        """Test main function with audit command."""
        # Arrange
        mock_args = MagicMock()
        mock_args.command = "audit"
        mock_parse_args.return_value = mock_args

        # Act
        with patch("common_utils.secrets.cli.handle_audit") as mock_handle_audit:
            main()

        # Assert
        mock_handle_audit.assert_called_once_with(mock_args)

    @patch("common_utils.secrets.cli.parse_args")
    def test_main_rotation_command(self, mock_parse_args):
        """Test main function with rotation command."""
        # Arrange
        mock_args = MagicMock()
        mock_args.command = "rotation"
        mock_parse_args.return_value = mock_args

        # Act
        with patch("common_utils.secrets.cli.handle_rotation") as mock_handle_rotation:
            main()

        # Assert
        mock_handle_rotation.assert_called_once_with(mock_args)

    @patch("common_utils.secrets.cli._check_auth", return_value=True)
    def test_handle_audit(self, mock_check_auth):
        """Test handle_audit function."""
        # Arrange
        args = MagicMock()
        args.directory = "test_dir"
        args.output = "test_output.txt"
        args.json = False
        args.exclude = ["exclude_dir"]

        # Act
        with patch("common_utils.secrets.cli.SecretsAuditor") as mock_auditor_class, \
             patch("sys.exit"):
            mock_auditor = MagicMock()
            mock_auditor_class.return_value = mock_auditor
            handle_audit(args)

        # Assert
        mock_auditor_class.assert_called_once_with(
            exclude_dirs=["exclude_dir"]
        )
        mock_auditor.audit.assert_called_once_with(
            directory="test_dir",
            output_file="test_output.txt",
            format="text"
        )

    @patch("common_utils.secrets.cli._check_auth", return_value=True)
    def test_handle_rotation(self, mock_check_auth):
        """Test handle_rotation function."""
        # Arrange
        args = MagicMock()
        args.backend = "env"
        args.rotation_command = "schedule"

        # Act
        with patch("common_utils.secrets.cli.SecretRotation") as mock_rotation_class, \
             patch("common_utils.secrets.cli._handle_schedule_rotation") as mock_handle_schedule:
            mock_rotation = MagicMock()
            mock_rotation_class.return_value = mock_rotation

            handle_rotation(args)

        # Assert
        mock_rotation_class.assert_called_once_with(secrets_backend=args.backend)
        mock_handle_schedule.assert_called_once()

    def test_handle_schedule_rotation(self):
        """Test _handle_schedule_rotation function."""
        # Arrange
        rotation = MagicMock()
        args = MagicMock()
        args.key = "test_key"
        args.interval = 30
        masked_key = "masked_key"

        # Act
        with patch("common_utils.secrets.cli.logger") as mock_logger:
            _handle_schedule_rotation(rotation, args, masked_key)

        # Assert
        rotation.schedule_rotation.assert_called_once_with("test_key", 30)
        mock_logger.info.assert_any_call("Scheduled rotation for %s every %d days", masked_key, 30)

    def test_handle_rotate_secret(self):
        """Test _handle_rotate_secret function."""
        # Arrange
        rotation = MagicMock()
        args = MagicMock()
        args.key = "test_key"
        masked_key = "masked_key"

        # Patch get_secret_value to return a value
        with patch("common_utils.secrets.cli.get_secret_value", return_value="secret_value"), \
             patch("common_utils.secrets.cli.logger") as mock_logger, \
             patch("sys.exit"):
            _handle_rotate_secret(rotation, args, masked_key)

        # Assert
        rotation.rotate_secret.assert_called_once_with("test_key", "secret_value")
        mock_logger.info.assert_any_call("Rotated secret %s", masked_key)

    def test_handle_rotate_all(self):
        """Test _handle_rotate_all function."""
        # Arrange
        rotation = MagicMock()
        # Mock the return value to be a tuple (count, [keys])
        rotation.rotate_all_due.return_value = (3, ["key1", "key2", "key3"])

        with patch("common_utils.secrets.cli.logger") as mock_logger, \
             patch("common_utils.secrets.cli.mask_sensitive_data", side_effect=lambda x: x):
            _handle_rotate_all(rotation)

        # Assert
        rotation.rotate_all_due.assert_called_once()
        mock_logger.info.assert_any_call("Rotated %d secrets:", 3)
        mock_logger.info.assert_any_call("  %s", "key1")
        mock_logger.info.assert_any_call("  %s", "key2")
        mock_logger.info.assert_any_call("  %s", "key3")

    def test_handle_list_due(self):
        """Test _handle_list_due function."""
        # Arrange
        rotation = MagicMock()
        rotation.get_due_secrets.return_value = ["key1", "key2"]

        with patch("common_utils.secrets.cli.logger") as mock_logger, \
             patch("common_utils.secrets.cli.mask_sensitive_data", side_effect=lambda x: x):
            _handle_list_due(rotation)

        # Assert
        rotation.get_due_secrets.assert_called_once()
        # There should be 4 info calls: 2 headers, 2 keys
        assert mock_logger.info.call_count == 4
        mock_logger.info.assert_any_call("Found %d secrets due for rotation", 2)
        mock_logger.info.assert_any_call("Found %d secrets due for rotation:", 2)
        mock_logger.info.assert_any_call("  %s", "key1")
        mock_logger.info.assert_any_call("  %s", "key2")

    def test_handle_unknown_rotation_command(self):
        """Test _handle_unknown_rotation_command function."""
        # Act
        with patch("common_utils.secrets.cli.logger") as mock_logger, \
             patch("sys.exit") as mock_exit:
            _handle_unknown_rotation_command("unknown_command")

        # Assert
        # Check for the specific error message
        mock_logger.error.assert_any_call("Unknown rotation command: unknown_command")
        mock_exit.assert_called_once_with(1)

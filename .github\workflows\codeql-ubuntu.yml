name: "CodeQL Analysis (Ubuntu)"

# This workflow performs CodeQL analysis for JavaScript/TypeScript and Python on Ubuntu
# It's a specialized version of the main CodeQL workflow for Ubuntu-specific analysis

on:
  push:
    branches: [ main, dev, master, develop ]
    paths-ignore:
      - '**/*.md'
      - '**/*.txt'
      - '**/*.rst'
      - '**/*.png'
      - '**/*.jpg'
      - '**/*.jpeg'
      - '**/*.gif'
      - '**/*.svg'
      - '**/*.ico'
      - '.gitignore'
      - 'docs/**'
      - 'LICENSE'
      - 'SECURITY.md'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
  pull_request:
    branches: [ main, dev, master, develop ]
    paths-ignore:
      - '**/*.md'
      - '**/*.txt'
      - '**/*.rst'
      - '**/*.png'
      - '**/*.jpg'
      - '**/*.jpeg'
      - '**/*.gif'
      - '**/*.svg'
      - '**/*.ico'
      - '.gitignore'
      - 'docs/**'
      - 'LICENSE'
      - 'SECURITY.md'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
  schedule:
    - cron: '0 4 * * 1'  # Weekly on Monday at 4 AM UTC (off-peak hours)
  workflow_dispatch:  # Allow manual triggering
  workflow_run:
    workflows: ["Gradual Lint Check"]
    types:
      - completed

# Limit concurrent runs to conserve resources
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  # Required for all workflows
  security-events: write
  # Only needed for workflows in private repositories
  actions: read
  contents: read

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    timeout-minutes: 90  # Increased timeout for larger codebases
    # Only run if auto-fix workflow completed successfully, or if triggered by other events
    if: ${{ github.event_name != 'workflow_run' || github.event.workflow_run.conclusion == 'success' }}

    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript-typescript', 'python' ]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full git history for better analysis

      # Set up pnpm first for JavaScript/TypeScript analysis
      - name: Setup pnpm
        if: matrix.language == 'javascript-typescript'
        uses: pnpm/action-setup@v4
        with:
          version: '8.15.4'
          run_install: false

      # Set up Node.js for JavaScript/TypeScript analysis
      - name: Set up Node.js
        if: matrix.language == 'javascript-typescript'
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      # Add pnpm to PATH for Linux/macOS
      - name: Add pnpm to PATH
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          # Add both global and local pnpm to PATH
          echo "PATH=$(pnpm -g bin):$(pnpm bin):$PATH" >> $GITHUB_ENV
          # Verify pnpm is in PATH
          echo "Current PATH: $PATH"
          which pnpm || {
            echo "pnpm not found in PATH. Installing globally with npm..."
            npm install -g pnpm
            echo "PATH=$(npm bin -g):$PATH" >> $GITHUB_ENV
            which pnpm || { echo "Error: pnpm is still not in PATH after npm install."; exit 1; }
          }

      # Verify Node.js and pnpm installation
      - name: Verify Node.js and pnpm installation
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          echo "Node.js version: $(node --version)"
          echo "npm version: $(npm --version)"
          if command -v pnpm &>/dev/null; then
            echo "pnpm version: $(pnpm --version)"
          else
            echo "pnpm not found, will use npm instead"
          fi

      # Set up Python for Python analysis
      - name: Set up Python
        if: matrix.language == 'python'
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'pip'

      # Display disk space before analysis
      - name: Check disk space
        shell: bash
        run: df -h

      # Verify lock files exist before analysis
      - name: Verify lock files
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          echo "Verifying lock files exist before CodeQL analysis..."
          LOCK_FILES=$(find . -name "package-lock.json" -o -name "yarn.lock" -o -name "pnpm-lock.yaml")

          if [ -z "$LOCK_FILES" ]; then
            echo "Warning: No lock files found. Creating a minimal package-lock.json in the root directory."

            # Create a minimal valid package-lock.json with echo
            echo '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3,"packages":{"":{"name":"paissive-income","version":"1.0.0"}}}' > package-lock.json

            echo "Created minimal package-lock.json in the root directory."

            # Verify the file was created
            if [ -f "package-lock.json" ]; then
              echo "Verification: package-lock.json exists and contains:"
              cat package-lock.json

              # Add to .gitignore if it exists
              if [ -f ".gitignore" ]; then
                if ! grep -q "package-lock.json" .gitignore; then
                  echo -e "\n# Generated during CodeQL analysis\npackage-lock.json" >> .gitignore
                  echo "Added package-lock.json to .gitignore"
                fi
              fi
            else
              echo "CRITICAL ERROR: Failed to create package-lock.json"
              # Try alternative method
              node -e "fs.writeFileSync('package-lock.json', JSON.stringify({name:'paissive-income',version:'1.0.0',lockfileVersion:3,packages:{'':{'name':'paissive-income','version':'1.0.0'}}}))" || true
              if [ -f "package-lock.json" ]; then
                echo "Successfully created package-lock.json using Node.js"
              fi
            fi
          else
            echo "Found lock files:"
            echo "$LOCK_FILES"
          fi
        continue-on-error: true

      # Install dependencies for JavaScript/TypeScript
      - name: Install Node.js dependencies
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          # Function to install dependencies with fallback
          install_deps() {
            local dir=$1
            local original_dir=$(pwd)

            if [ -n "$dir" ]; then
              cd "$dir" || return 1
            fi

            echo "Installing dependencies in $(pwd)..."

            # Try with pnpm first (preferred)
            if command -v pnpm &>/dev/null; then
              echo "Using pnpm to install dependencies..."
              pnpm install || {
                echo "pnpm install failed, trying with npm..."
                npm install || {
                  echo "Both pnpm and npm installation failed in $(pwd)"
                  if [ -n "$dir" ]; then
                    cd "$original_dir"
                  fi
                  return 1
                }
              }
            else
              # Fallback to npm if pnpm is not available
              echo "pnpm not found, using npm..."
              npm install || {
                echo "npm install failed in $(pwd)"
                if [ -n "$dir" ]; then
                  cd "$original_dir"
                fi
                return 1
              }
            fi

            if [ -n "$dir" ]; then
              cd "$original_dir"
            fi

            return 0
          }

          # Install dependencies in root directory if package.json exists
          if [ -f "package.json" ]; then
            echo "Found package.json in root directory"
            install_deps
          fi

          # Install dependencies in ui/react_frontend if package.json exists
          if [ -f "ui/react_frontend/package.json" ]; then
            echo "Found package.json in ui/react_frontend"
            install_deps "ui/react_frontend"
          fi

          # Install dependencies in sdk/javascript if package.json exists
          if [ -f "sdk/javascript/package.json" ]; then
            echo "Found package.json in sdk/javascript"
            install_deps "sdk/javascript"
          fi
        continue-on-error: true

      # Clean up node_modules to save space
      - name: Clean up node_modules
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          echo "Cleaning up node_modules to save space..."
          find . -name "node_modules" -type d -prune -exec rm -rf {} \; 2>/dev/null || true
        continue-on-error: true

      # Ensure CodeQL configuration files exist
      - name: Ensure CodeQL configuration files
        shell: bash
        run: |
          if [ -f ".github/scripts/ensure-codeql-configs.sh" ]; then
            echo "Running ensure-codeql-configs.sh script..."
            bash .github/scripts/ensure-codeql-configs.sh
          else
            echo "ensure-codeql-configs.sh script not found. Creating minimal configuration..."

            # Ensure directory exists
            mkdir -p .github/codeql

            # Create minimal Ubuntu configuration
            echo 'name: "CodeQL Configuration for Ubuntu"' > .github/codeql/security-os-ubuntu.yml
            echo 'os: ubuntu-latest' >> .github/codeql/security-os-ubuntu.yml
            echo 'queries:' >> .github/codeql/security-os-ubuntu.yml
            echo '  - uses: security-and-quality' >> .github/codeql/security-os-ubuntu.yml
            echo '  - uses: security-extended' >> .github/codeql/security-os-ubuntu.yml
            echo '  - uses: security' >> .github/codeql/security-os-ubuntu.yml
            echo 'disable-default-queries: false' >> .github/codeql/security-os-ubuntu.yml
            echo 'trap-for-errors: true' >> .github/codeql/security-os-ubuntu.yml

            # Create minimal unified configuration
            echo 'name: "Unified CodeQL Configuration"' > .github/codeql/security-os-config.yml
            echo 'queries:' >> .github/codeql/security-os-config.yml
            echo '  - uses: security-and-quality' >> .github/codeql/security-os-config.yml
            echo '  - uses: security-extended' >> .github/codeql/security-os-config.yml
            echo '  - uses: security' >> .github/codeql/security-os-config.yml
            echo 'disable-default-queries: false' >> .github/codeql/security-os-config.yml
            echo 'trap-for-errors: true' >> .github/codeql/security-os-config.yml
          fi

      # Initialize CodeQL
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          queries: security-and-quality
          config-file: .github/codeql/security-os-ubuntu.yml
          debug: true

      # Autobuild attempts to build any compiled languages
      - name: Autobuild
        uses: github/codeql-action/autobuild@v3
        continue-on-error: true

      # Verify environment before CodeQL analysis
      - name: Verify environment before analysis
        shell: bash
        run: |
          echo "Verifying environment before CodeQL analysis..."

          # Create sarif-results directory if it doesn't exist
          if [ ! -d "sarif-results" ]; then
            echo "Creating sarif-results directory..."
            mkdir -p sarif-results
          fi

          # JavaScript/TypeScript specific checks
          if [ "${{ matrix.language }}" = "javascript-typescript" ]; then
            # Final check for package-lock.json - create it if it still doesn't exist
            if [ ! -f "package-lock.json" ]; then
              echo "WARNING: package-lock.json still not found! Creating it as a last resort..."
              echo '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3,"packages":{"":{"name":"paissive-income","version":"1.0.0"}}}' > package-lock.json
              echo "Created minimal package-lock.json in the root directory."
            fi

            # Verify package-lock.json
            if [ -f "package-lock.json" ]; then
              echo "package-lock.json exists and contains:"
              cat package-lock.json
            else
              echo "CRITICAL ERROR: package-lock.json still not found after multiple attempts!"
            fi
          fi

          # Python specific checks
          if [ "${{ matrix.language }}" = "python" ]; then
            echo "Verifying Python environment..."
            python --version
            pip --version
          fi

          echo "Environment verification complete."

      # Perform CodeQL Analysis with enhanced error handling
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{ matrix.language }}"
          upload: true
          output: sarif-results/${{ matrix.language }}-ubuntu.sarif
        continue-on-error: false # Fail the workflow if CodeQL analysis fails

      # Upload SARIF results as an artifact
      - name: Upload SARIF results
        uses: actions/upload-artifact@v4
        if: always() # Always attempt to upload artifacts even if previous steps fail
        with:
          name: ${{ matrix.language }}-ubuntu-sarif
          path: sarif-results/${{ matrix.language }}-ubuntu.sarif
          retention-days: 7
          if-no-files-found: warn # Warn but don't fail if no files are found

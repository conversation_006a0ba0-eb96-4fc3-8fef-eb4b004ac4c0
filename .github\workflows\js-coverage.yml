name: JS Test Coverage

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:
  workflow_run:
    workflows: ["Gradual Lint Check"]
    types:
      - completed
    # Codecov will comment on PRs with coverage changes if configured in the Codecov dashboard.

jobs:
  js-coverage:
    if: ${{ github.event_name != 'workflow_run' || github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20, 24]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8
          run_install: false

      - name: Install dependencies
        run: pnpm install

      - name: Create coverage directory
        run: mkdir -p coverage

      - name: Build Tailwind CSS
        run: pnpm tailwind:build

      - name: Run JS tests and check coverage
        run: |
          echo "Node.js version: $(node --version)"
          echo "npm version: $(npm --version)"
          echo "pnpm version: $(pnpm --version)"
          pnpm test:ci --passWithNoTests || true

      - name: Generate coverage report
        run: pnpm coverage > ./coverage/lcov.info || echo "Failed to generate coverage report, but continuing workflow"

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          files: ./coverage/lcov.info
          flags: js, node${{ matrix.node-version }}
          fail_ci_if_error: false

      - name: Upload HTML coverage report as artifact
        uses: actions/upload-artifact@v4
        with:
          name: js-coverage-html-report-node${{ matrix.node-version }}
          path: coverage/
          if-no-files-found: warn

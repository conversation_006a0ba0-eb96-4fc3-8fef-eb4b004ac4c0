name: Docker Compose Integration

on:
  push:
    branches: [ main, develop, cosine/add/ag-ui-pnpm-zt2ztr ]
    paths:
      - 'docker-compose*.yml'
      - 'scripts/fix-docker-*.sh'
      - 'scripts/run-docker-compose-ci.sh'
      - '.github/workflows/docker-compose-workflow.yml'
      - 'ui/react_frontend/Dockerfile.dev'
      - 'ui/react_frontend/package.json'
      - 'Dockerfile'
      - 'docker-healthcheck.sh'
      - 'wait-for-db.sh'
  pull_request:
    branches: [ main, develop, master ]
    paths:
      - 'docker-compose*.yml'
      - 'scripts/fix-docker-*.sh'
      - 'scripts/run-docker-compose-ci.sh'
      - '.github/workflows/docker-compose-workflow.yml'
      - 'ui/react_frontend/Dockerfile.dev'
      - 'ui/react_frontend/package.json'
      - 'Dockerfile'
      - 'docker-healthcheck.sh'
      - 'wait-for-db.sh'
  workflow_dispatch:  # Allow manual triggering
  workflow_run:
    workflows: ["Gradual Lint Check"]
    types:
      - completed
    branches:
      - main
      - develop
      - master
      - cosine/add/ag-ui-pnpm-zt2ztr

jobs:
  docker-compose-integration:
    if: ${{ github.event_name != 'workflow_run' || github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    # Use larger runner for better performance
    env:
      DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
      # Enable BuildKit for faster builds
      DOCKER_BUILDKIT: 1
      COMPOSE_DOCKER_CLI_BUILD: 1
      # Set CI flag for optimized builds
      CI: true
      # Optimize Docker layer caching
      BUILDKIT_PROGRESS: plain
      # Reduce log verbosity
      BUILDKIT_INLINE_CACHE: 1

    steps:
      - name: Optimize runner for Docker workloads
        run: |
          echo "Initial disk space and memory:"
          df -h
          free -m

          # More aggressive cleanup to free up space
          echo "Performing aggressive cleanup..."
          sudo rm -rf /usr/share/dotnet /usr/local/lib/android /opt/ghc /usr/local/.ghcup || true
          sudo rm -rf /usr/local/share/boost /usr/local/share/rust /usr/share/swift || true
          sudo rm -rf /tmp/* /var/tmp/* || true
          sudo apt-get clean
          sudo apt-get autoremove -y

          # Optimize Docker configuration for performance
          echo "Optimizing Docker configuration..."
          echo '{
            "builder": { "gc": { "enabled": true, "defaultKeepStorage": "20GB" } },
            "experimental": true,
            "features": { "buildkit": true },
            "registry-mirrors": ["https://mirror.gcr.io"]
          }' | sudo tee /etc/docker/daemon.json
          sudo systemctl restart docker || true

          # Prune Docker system
          echo "Pruning Docker system..."
          docker system prune -a -f --volumes

          echo "Disk space after optimization:"
          df -h

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          # Shallow clone for faster checkout
          fetch-depth: 1

      - name: Set up Python and detect frontend
        id: setup
        run: |
          # Install Python 3.12 directly (faster than actions/setup-python)
          sudo apt-get update -q
          sudo apt-get install -y --no-install-recommends python3 python3-venv python3-dev

          # Create symlink to make it the default python
          sudo ln -sf /usr/bin/python3 /usr/bin/python

          # Verify Python version
          python3 --version

          # Check for frontend in parallel
          if [ -d "ui/react_frontend" ]; then
            echo "has_frontend=true" >> $GITHUB_OUTPUT
          else
            echo "has_frontend=false" >> $GITHUB_OUTPUT
          fi

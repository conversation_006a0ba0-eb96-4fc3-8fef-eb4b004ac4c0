name: Build Tailwind CSS

on:
  push:
    branches: [main]
    paths:
      - "ui/static/css/tailwind.css"
      - "tailwind.config.js"
      - "package.json"
      - ".github/workflows/tailwind-build.yml"
  pull_request:
    branches: [main]
    paths:
      - "ui/static/css/tailwind.css"
      - "tailwind.config.js"
      - "package.json"
      - ".github/workflows/tailwind-build.yml"
  workflow_dispatch:
  workflow_run:
    workflows: ["Gradual Lint Check"]
    types:
      - completed
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        run: npm install -g pnpm@8.15.4

      - name: Install dependencies
        run: pnpm install

      - name: Build Tailwind CSS
        run: pnpm tailwind:build

      - name: Verify Tailwind CSS output
        run: |
          echo "Checking if Tailwind CSS output file exists..."
          if [ -f "ui/static/css/tailwind.output.css" ]; then
            echo "✅ Tailwind CSS output file found"
            ls -la ui/static/css/tailwind.output.css
          else
            echo "❌ Tailwind CSS output file not found"
            echo "Contents of ui/static/css directory:"
            ls -la ui/static/css/
            exit 1
          fi

      - name: Upload Tailwind CSS output as artifact
        uses: actions/upload-artifact@v4
        if: ${{ github.event_name != 'workflow_run' || github.event.workflow_run.conclusion == 'success' }}
        with:
          name: tailwind-output-css
          path: ui/static/css/tailwind.output.css
          if-no-files-found: warn

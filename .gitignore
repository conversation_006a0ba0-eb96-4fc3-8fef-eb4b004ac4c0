#  Usually these files are written by a python packager
# Added by <PERSON> Master
# Byte-compiled / optimized / DLL files
# C extensions
# CodeQL
# Distribution / packaging
# Django stuff:
# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
pythonenv*
.python-version
pyvenv.cfg
pip-selfcheck.json
# Flask stuff:
# IDE specific files
# IPython
# Installer logs
# Jupyter Notebook
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
*.tmp
*.bak
*.swp
*~
temp/
tmp/
# Node.js
# OS specific
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.lnk
._*
.Spotlight-V100
.Trashes
# Playwright reports
# Project specific
# PyBuilder
# PyInstaller
# Pyre type checker
# Rope project settings
# SageMath parsed files
# Scrapy stuff:
# Sphinx documentation
# Spyder project settings
# Task files
# Translations
# Unit test / coverage reports
# celery beat schedule file
# mkdocs documentation
# mypy
# pyenv
*$py.class
pytest.ini.temp
pytest_temp.ini
**/.env/
**/.venv/
**/dist-packages/
**/env/
**/node_modules/
**/site-packages/
**/venv/
**/virtualenv/
*.cover
*.egg
*.egg-info/
*.log
*.manifest
*.mo
*.pot
*.py[cod]
*.sage.py
*.sarif
!security-reports/*.sarif
*.sarif.json
*coverage_helper.coveragerc

# Explicit ignore for security/test artifacts
bandit-output.json
bandit-results.json
!security-reports/bandit-results.json
bandit-results-ini.json
!security-reports/bandit-results-ini.json
bandit-results-*.json
bandit-results-*.txt
test-bandit-*.json
test_bandit_sample.py
run_tests_bandit.json
security-reports/current-bandit-results.json
security-reports/bandit-results-*.sarif
!security-reports/bandit-results.sarif
!security-reports/bandit-results-ini.sarif
bandit-results.json.tmp
*.json.tmp
coverage-*.xml
junit/test-results-*.xml
*-results-*.json
*-results-*.xml
secrets.sarif.json
empty-sarif.json
.pytest_cache/
.ruff_cache/
.mypy_cache/
.coverage
coverage.xml
htmlcov/
create_bandit_files.sh

# Test coverage reports
coverage/
coverage*/
.coverage
coverage.xml
coverage-*.xml
htmlcov/

# Test reports
test-output/
playwright-report/
**/playwright-report/
test-results/
**/test-results/

# Editor and IDE files
.vscode/
.idea/
.cursor/
*.code-workspace
.cursor.json
.cursor/*.json
mcp.json

# Build artifacts and temporary files
*.new
*.old
*.temp
*.tmp

# Editor/tool-specific configs
.cursor/
cline_mcp_settings.json
pyrefly.toml
.vscode/
.idea/
*.iml
*.iws
*.ipr
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates
*.sublime-workspace
*.sublime-project
*.so
*.spec
*.swo
*.swp
*.tmp
*env*/
*venv*/
.DS_Store
.Python
.aider*
.cache
.coverage
.coverage.*
.dmypy.json
.eggs/
.env
.env/
.hypothesis/
.idea/
.installed.cfg
.ipynb_checkpoints
.mypy_cache/
.nox/
.npm
.pnpm-debug.log
.pnpm-store/
.pyre/
.pytest_cache/
.python-version
.ropeproject
.scrapy
.spyderproject
.spyproject
.tox/
.venv
.venv/
.vscode/
.webassets-cache
.yarn
/site
ENV/
MANIFEST
Thumbs.db
__pycache__/
build/
celerybeat-schedule
coverage.xml
db.sqlite3
dev-debug.log
develop-eggs/
dist/
dmypy.json
docs/_build/
downloads/
eggs/
env.bak/
env/
example_project_plan.json
htmlcov/
coverage/
instance/
ipython_config.py
lib/
lib64/
local_settings.py
logs
node_modules/
nosetests.xml
npm-debug.log
parts/
pip-delete-this-directory.txt
pip-log.txt
profile_default/
project_plan.json
sarif-results/
sdist/
target/
tasks.json
tasks/
ui/react_frontend/playwright-report/
var/
venv.bak/
venv/
virtualenv/
wheels/
yarn-debug.log
yarn-error.log
# Ignore generated Tailwind CSS build artifacts
ui/static/css/tailwind.output.css
ui/react_frontend/src/tailwind.output.css

# Ignore mypy stubs
mypy_stubs/
mypy_stubs/modelcontextprotocol/
mypy_stubs/psycopg2/

# Ignore package lock files
.nyc_output/
package-lock.json
sdk/javascript/package-lock.json

# CI/CD artifacts and temporary files
ci-mode-active.txt
ci-reports/
**/ci-mode-active.txt
**/ci-reports/

# ARTIST experiment outputs – exclude from version control
artist_experiments/data/
artist_experiments/models/
artist_experiments/logs/

# Environment reports that may contain sensitive information
**/environment-report.json
**/ci-reports/**/environment-report.json

# Coverage reports
coverage*.txt
*_cov.txt

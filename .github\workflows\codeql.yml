name: "CodeQL Analysis"

# This workflow performs CodeQL analysis for JavaScript/TypeScript and Python
# It runs on push to main branches, pull requests to main branches, and on a weekly schedule
# The workflow is designed to provide comprehensive security analysis with optimized timeouts

on:
  push:
    branches: [ main, dev, master, develop ]
    paths-ignore:
      - '**/*.md'
      - '**/*.txt'
      - '**/*.rst'
      - '**/*.png'
      - '**/*.jpg'
      - '**/*.jpeg'
      - '**/*.gif'
      - '**/*.svg'
      - '**/*.ico'
      - '.gitignore'
      - 'docs/**'
      - 'LICENSE'
      - 'SECURITY.md'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
  pull_request:
    branches: [ main, dev, master, develop ]
    paths-ignore:
      - '**/*.md'
      - '**/*.txt'
      - '**/*.rst'
      - '**/*.png'
      - '**/*.jpg'
      - '**/*.jpeg'
      - '**/*.gif'
      - '**/*.svg'
      - '**/*.ico'
      - '.gitignore'
      - 'docs/**'
      - 'LICENSE'
      - 'SECURITY.md'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
  schedule:
    - cron: '0 4 * * 1'  # Weekly on Monday at 4 AM UTC (off-peak hours)
  workflow_dispatch:  # Allow manual triggering
  workflow_run:
    workflows: ["Gradual Lint Check"]
    types:
      - completed
    branches:
      - main
      - dev
      - master
      - develop

# Limit concurrent runs to conserve resources
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  # Required for all workflows
  security-events: write
  # Only needed for workflows in private repositories
  actions: read
  contents: read

jobs:
  analyze:
    name: Analyze
    # Use Ubuntu as the primary runner for this workflow
    # Platform-specific analysis is handled by dedicated workflows
    runs-on: ubuntu-latest
    timeout-minutes: 90  # Increased timeout for larger codebases
    # Only run if auto-fix workflow completed successfully, or if triggered by other events
    if: ${{ github.event_name != 'workflow_run' || github.event.workflow_run.conclusion == 'success' }}
    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript-typescript', 'python' ]
    env:
      ACTIONS_RUNNER_DEBUG: true
      ACTIONS_STEP_DEBUG: true

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full git history for better analysis

      # Set up pnpm first for JavaScript/TypeScript analysis
      - name: Setup pnpm
        if: matrix.language == 'javascript-typescript'
        uses: pnpm/action-setup@v4
        with:
          version: '8.15.4'
          run_install: false

      # Set up Node.js for JavaScript/TypeScript analysis
      - name: Set up Node.js
        if: matrix.language == 'javascript-typescript'
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      # Add pnpm to PATH for Linux/macOS
      - name: Add pnpm to PATH (Linux/macOS)
        if: matrix.language == 'javascript-typescript' && runner.os != 'Windows'
        shell: bash
        run: |
          # Add both global and local pnpm to PATH
          echo "PATH=$(pnpm -g bin):$(pnpm bin):$PATH" >> $GITHUB_ENV
          # Verify pnpm is in PATH
          echo "Current PATH: $PATH"
          which pnpm || {
            echo "pnpm not found in PATH. Installing globally with npm..."
            npm install -g pnpm
            echo "PATH=$(npm bin -g):$PATH" >> $GITHUB_ENV
            which pnpm || { echo "Error: pnpm is still not in PATH after npm install."; exit 1; }
          }

      # Add pnpm to PATH for Windows
      - name: Add pnpm to PATH (Windows)
        if: matrix.language == 'javascript-typescript' && runner.os == 'Windows'
        shell: pwsh
        run: |
          try {
            # Add pnpm to PATH
            $pnpmPath = "$env:LOCALAPPDATA\\pnpm"
            if (Test-Path $pnpmPath) {
              echo "Adding $pnpmPath to PATH"
              echo "$pnpmPath" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append
            }

            # Verify pnpm is available
            $pnpmVersion = pnpm --version
            echo "pnpm version: $pnpmVersion"
          } catch {
            echo "Error setting up pnpm: $_"
            echo "Installing pnpm globally with npm..."
            npm install -g pnpm

            # Add npm global bin to PATH
            $npmBin = npm bin -g
            echo "Adding npm global bin to PATH: $npmBin"
            echo "$npmBin" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

            # Verify pnpm is now available
            try {
              $pnpmVersion = pnpm --version
              echo "pnpm version after npm install: $pnpmVersion"
            } catch {
              echo "pnpm still not available after npm install. Continuing with npm only."
            }
          }

      # Verify Node.js and pnpm installation
      - name: Verify Node.js and pnpm installation
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          echo "Node.js version: $(node --version)"
          echo "npm version: $(npm --version)"
          if command -v pnpm &>/dev/null; then
            echo "pnpm version: $(pnpm --version)"
          else
            echo "pnpm not found, will use npm instead"
          fi

      # Set up Python for Python analysis
      - name: Set up Python
        if: matrix.language == 'python'
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'pip'

      # Install Python dependencies
      - name: Install Python dependencies
        if: matrix.language == 'python'
        shell: bash
        run: |
          python -m pip install --upgrade pip
          echo "Installing Python dependencies from requirements.txt..."
          if [ -f requirements.txt ]; then
            pip install -r requirements.txt
          else
            echo "requirements.txt not found, skipping."
          fi
          echo "Installing project in editable mode (if setup.py or pyproject.toml exists)..."
          if [ -f setup.py ] || [ -f pyproject.toml ]; then
            python -m pip install --upgrade setuptools wheel
            pip install -e .
          else
            echo "No setup.py or pyproject.toml found, skipping project installation."
          fi
          echo "Python dependency installation step complete."

      # Display disk space before analysis
      - name: Check disk space
        shell: bash
        run: df -h

      # Verify lock files exist before analysis
      - name: Verify lock files
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          echo "Verifying lock files exist before CodeQL analysis..."
          LOCK_FILES=$(find . -name "package-lock.json" -o -name "yarn.lock" -o -name "pnpm-lock.yaml")

          if [ -z "$LOCK_FILES" ]; then
            echo "Warning: No lock files found. Creating a minimal package-lock.json in the root directory."

            # Create a minimal valid package-lock.json with echo
            echo '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3,"packages":{"":{}}}' > package-lock.json

            echo "Created minimal package-lock.json in the root directory."

            # Verify the file was created
            if [ -f "package-lock.json" ]; then
              echo "Verification: package-lock.json exists and contains:"
              cat package-lock.json

              # Add to .gitignore if it exists
              if [ -f ".gitignore" ]; then
                if ! grep -q "package-lock.json" .gitignore; then
                  echo -e "\n# Generated during CodeQL analysis\npackage-lock.json" >> .gitignore
                  echo "Added package-lock.json to .gitignore"
                fi
              fi
            else
              echo "CRITICAL ERROR: Failed to create package-lock.json"
              # Try alternative method
              node -e "fs.writeFileSync('package-lock.json', JSON.stringify({name:'paissive-income',version:'1.0.0',lockfileVersion:3,packages:{'':{name:'paissive-income',version:'1.0.0'}}}))" || true
              if [ -f "package-lock.json" ]; then
                echo "Successfully created package-lock.json using Node.js"
              fi
            fi
          else
            echo "Found lock files:"
            echo "$LOCK_FILES"
          fi

      # Install dependencies for JavaScript/TypeScript with timeout
      - name: Install Node.js dependencies
        if: matrix.language == 'javascript-typescript'
        shell: bash
        timeout-minutes: 10  # Add timeout for dependency installation
        run: |
          # Function to install dependencies with fallback
          install_deps() {
            local dir=$1
            local original_dir=$(pwd)

            if [ -n "$dir" ]; then
              cd "$dir" || return 1
            fi

            echo "Installing dependencies in $(pwd)..."

            # Try with pnpm first (preferred)
            if command -v pnpm &>/dev/null; then
              echo "Using pnpm to install dependencies..."
              timeout 8m pnpm install || {
                echo "pnpm install failed or timed out, trying with npm..."
                timeout 8m npm install || {
                  echo "Both pnpm and npm installation failed in $(pwd)"
                  if [ -n "$dir" ]; then
                    cd "$original_dir"
                  fi
                  return 1
                }
              }
            else
              # Fallback to npm if pnpm is not available
              echo "pnpm not found, using npm..."
              timeout 8m npm install || {
                echo "npm install failed in $(pwd)"
                if [ -n "$dir" ]; then
                  cd "$original_dir"
                fi
                return 1
              }
            fi

            if [ -n "$dir" ]; then
              cd "$original_dir"
            fi

            return 0
          }

          # Install dependencies in root directory if package.json exists
          if [ -f "package.json" ]; then
            echo "Found package.json in root directory"
            install_deps
          fi

          # Install dependencies in ui/react_frontend if package.json exists
          if [ -f "ui/react_frontend/package.json" ]; then
            echo "Found package.json in ui/react_frontend"
            install_deps "ui/react_frontend"
          fi

          # Install dependencies in sdk/javascript if package.json exists
          if [ -f "sdk/javascript/package.json" ]; then
            echo "Found package.json in sdk/javascript"
            install_deps "sdk/javascript"
          fi
        continue-on-error: true

      # Clean up node_modules to save space
      - name: Clean up node_modules
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          echo "Cleaning up node_modules to save space..."
          find . -name "node_modules" -type d -prune -exec rm -rf {} \; 2>/dev/null || true
        continue-on-error: true

      # Ensure CodeQL configuration files exist
      - name: Ensure CodeQL configuration files
        shell: bash
        run: |
          if [ -f ".github/scripts/ensure-codeql-configs.sh" ]; then
            echo "Running ensure-codeql-configs.sh script..."
            timeout 5m bash .github/scripts/ensure-codeql-configs.sh
          elif [ -f ".github/scripts/ensure_codeql_configs.py" ]; then
            echo "Running ensure_codeql_configs.py script..."
            python .github/scripts/ensure_codeql_configs.py
          else
            echo "Ensure scripts not found. Creating minimal configuration..."
            # Ensure directory exists
            mkdir -p .github/codeql

            # Create minimal unified configuration
            cat > .github/codeql/security-os-config.yml << 'EOF'
            name: "Unified CodeQL Configuration"
            queries:
              - uses: security-and-quality
              - uses: security-extended
              - uses: security
            disable-default-queries: false
            trap-for-errors: true
          EOF
            # Create minimal qls files if they don't exist, pointing to security-and-quality
            if [ ! -f ".github/codeql/javascript-security-queries.qls" ]; then
              echo "- description: Minimal JS Security Queries\n- uses: security-and-quality" > .github/codeql/javascript-security-queries.qls
            fi
            if [ ! -f ".github/codeql/python-security-queries.qls" ]; then
              echo "- description: Minimal Python Security Queries\n- uses: security-and-quality" > .github/codeql/python-security-queries.qls
            fi
          fi

      # Initialize CodeQL with timeout
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          config-file: .github/codeql/security-os-config.yml
          debug: false
        timeout-minutes: 10

      # Autobuild attempts to build any compiled languages with timeout
      - name: Autobuild
        uses: github/codeql-action/autobuild@v3
        timeout-minutes: 15
        continue-on-error: true

      # Verify environment before CodeQL analysis
      - name: Verify environment before analysis
        shell: bash
        run: |
          echo "Verifying environment before CodeQL analysis..."

          # Create sarif-results directory if it doesn't exist
          if [ ! -d "sarif-results" ]; then
            echo "Creating sarif-results directory..."
            mkdir -p sarif-results
          fi

          # JavaScript/TypeScript specific checks
          if [ "${{ matrix.language }}" = "javascript-typescript" ]; then
            # Final check for package-lock.json - create it if it still doesn't exist
            if [ ! -f "package-lock.json" ]; then
              echo "WARNING: package-lock.json still not found! Creating it as a last resort..."
              echo '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3,"packages":{"":{}}}' > package-lock.json
              echo "Created minimal package-lock.json in the root directory."
            fi

            # Verify package-lock.json
            if [ -f "package-lock.json" ]; then
              echo "package-lock.json exists and contains:"
              cat package-lock.json
            else
              echo "CRITICAL ERROR: package-lock.json still not found after multiple attempts!"
            fi
          fi

          # Python specific checks
          if [ "${{ matrix.language }}" = "python" ]; then
            echo "Verifying Python environment..."
            python --version
            pip --version
          fi

          echo "Environment verification complete."

      # Perform CodeQL Analysis with enhanced error handling and timeout
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{ matrix.language }}"
          upload: true
          output: sarif-results/${{ matrix.language }}-${{ runner.os }}.sarif
        timeout-minutes: 20  # Add timeout for analysis step
        continue-on-error: false # Fail the workflow if CodeQL analysis fails

      # Upload SARIF results as an artifact
      - name: Upload SARIF results
        uses: actions/upload-artifact@v4
        if: always() # Always attempt to upload artifacts even if previous steps fail
        with:
          name: ${{ matrix.language }}-${{ runner.os }}-sarif
          path: sarif-results/${{ matrix.language }}-${{ runner.os }}.sarif
          retention-days: 7
          if-no-files-found: warn # Warn but don't fail if no files are found

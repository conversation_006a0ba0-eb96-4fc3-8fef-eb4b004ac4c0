"""discovery_client - Module for services/service_discovery.discovery_client."""

# Standard library imports
import logging
from typing import Any, Dict, List, Optional

# Third-party imports
# Local imports
from services.service_discovery.load_balancer import LoadBalancer

# Configure logging


# Configure logging


# Configure logging


# Configure logging


# Configure logging


logger = logging.getLogger(__name__)


class ConsulServiceRegistry:
    """Service registry implementation using Consul."""

    def __init__(self, host: str = "localhost", port: int = 8500):
        """
        Initialize the Consul service registry.

        Args:
            host: Consul host
            port: Consul port

        """
        self.host = host
        self.port = port
        logger.info(f"Initialized Consul service registry at {host}:{port}")

    def register_service(
        self,
        service_name: str,
        port: int,
        health_check_path: str = "/health",
        tags: List[str] = None,
        metadata: Dict[str, str] = None,
    ) -> bool:
        """
        Register a service with the registry.

        Args:
            service_name: Name of the service
            port: Port the service is running on
            health_check_path: Path for health check endpoint
            tags: List of tags for the service (used for service filtering and discovery)
            metadata: Additional metadata for the service (used for service information)

        Returns:
            bool: True if registration was successful

        """
        # Fix for CodeQL "Unused local variable" issue
        # Initialize default values and actually use the variables
        service_tags = [] if tags is None else tags
        service_metadata = {} if metadata is None else metadata

        # Using module logger. Original intent for direct logging.info was for test mocking.
        logger.info(f"Registering service {service_name} on port {port}")
        # Additional logging that can be useful for debugging
        logger.info(f"Service tags: {service_tags}")
        logger.info(f"Service metadata: {service_metadata}")

        # In a real implementation, this would make an API call to Consul
        # with the tags and metadata included
        return True

    def deregister_service(self, service_id: str) -> bool:
        """
        Deregister a service from the registry.

        Args:
            service_id: ID of the service to deregister

        Returns:
            bool: True if deregistration was successful

        """
        # Using module logger. Original intent for direct logging.info was for test mocking.
        logger.info(f"Deregistering service {service_id}")
        # In a real implementation, this would make an API call to Consul
        return True

    def get_service_instances(self, service_name: str) -> List[Dict[str, Any]]:
        """
        Get all instances of a service.

        Args:
            service_name: Name of the service to look up

        Returns:
            List of service instances

        """
        # Using module logger. Original intent for direct logging.info was for test mocking.
        logger.info(f"Looking up instances for service {service_name}")
        # In a real implementation, this would make an API call to Consul
        # For testing purposes, return mock data
        if service_name == "test-service":
            return [
                {"id": "test-service-1", "address": "localhost", "port": 8001},
                {"id": "test-service-2", "address": "localhost", "port": 8002},
            ]
        return []


class ServiceDiscoveryClient:
    """Client for service discovery."""

    def __init__(
        self,
        service_name: str,
        port: int,
        auto_register: bool = True,
        registry_host: str = "localhost",
        registry_port: int = 8500,
        load_balancer_strategy: str = "round_robin",
    ):
        """
        Initialize the service discovery client.

        Args:
            service_name: Name of this service
            port: Port this service is running on
            auto_register: Whether to automatically register this service
            registry_host: Host of the service registry
            registry_port: Port of the service registry
            load_balancer_strategy: Strategy for load balancing

        """
        self.service_name = service_name
        self.port = port
        self.registry = ConsulServiceRegistry(registry_host, registry_port)
        self.load_balancer = LoadBalancer(strategy=load_balancer_strategy)

        if auto_register:
            self.register_service(
                service_name=service_name,
                port=port,
                health_check_path="/health",
            )

    def discover_service(self, service_name: str) -> List[Dict[str, Any]]:
        """
        Discover instances of a service.

        Args:
            service_name: Name of the service to discover

        Returns:
            List of service instances

        """
        return self.registry.get_service_instances(service_name)

    def get_service_url(self, service_name: str, path: str = "") -> Optional[str]:
        """
        Get a URL for a service using load balancing.

        Args:
            service_name: Name of the service
            path: Path to append to the URL

        Returns:
            URL for the service or None if no instances are available

        """
        instances = self.discover_service(service_name)
        if not instances:
            logger.warning(f"No instances found for service {service_name}")
            return None

        instance = self.load_balancer.select_instance(instances)
        if not instance:
            return None

        url = f"http://{instance['address']}:{instance['port']}{path}"
        return url

    def register_service(
        self,
        service_name: str,
        port: int,
        health_check_path: str = "/health",
        tags: List[str] = None,
        metadata: Dict[str, str] = None,
    ) -> bool:
        """
        Register a service with the registry.

        Args:
            service_name: Name of the service
            port: Port the service is running on
            health_check_path: Path for health check endpoint
            tags: List of tags for the service
            metadata: Additional metadata for the service

        Returns:
            bool: True if registration was successful

        """
        if tags is None:
            tags = []
        if metadata is None:
            metadata = {}

        return self.registry.register_service(
            service_name=service_name,
            port=port,
            health_check_path=health_check_path,
            tags=tags,
            metadata=metadata,
        )

    def deregister_service(self, service_id: str) -> bool:
        """
        Deregister a service from the registry.

        Args:
            service_id: ID of the service to deregister

        Returns:
            bool: True if deregistration was successful

        """
        return self.registry.deregister_service(service_id)

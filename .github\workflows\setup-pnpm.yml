name: Setup pnpm (Reusable)

on:
  workflow_call:
    inputs:
      pnpm-version:
        description: 'Version of pnpm to use'
        required: true
        type: string
        default: '8'
      node-version:
        description: 'Version of Node.js to use'
        required: true
        type: string
        default: '20'
      working-directory:
        description: 'Working directory to use'
        required: false
        type: string
        default: '.'
      verify-package-json:
        description: 'Whether to verify package.json exists'
        required: false
        type: boolean
        default: true
      create-if-missing:
        description: 'Whether to create package.json if missing'
        required: false
        type: boolean
        default: false
      install-dependencies:
        description: 'Whether to install dependencies'
        required: false
        type: boolean
        default: true
  workflow_dispatch:
  workflow_run:
    workflows: ["Gradual Lint Check"]
    types:
      - completed

jobs:
  setup-pnpm:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
    steps:
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ inputs.pnpm-version }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
          cache: 'pnpm'
          cache-dependency-path: pnpm-lock.yaml

      - name: Add pnpm to PATH
        shell: bash
        run: |
          # Get pnpm path
          PNPM_PATH=$(which pnpm)
          if [ -z "$PNPM_PATH" ]; then
            echo "pnpm not found in PATH, attempting to find it..."
            if [ "$RUNNER_OS" = "Windows" ]; then
              PNPM_PATH="$APPDATA/npm/pnpm.cmd"
              if [ ! -f "$PNPM_PATH" ]; then
                PNPM_PATH="$LOCALAPPDATA/pnpm/pnpm.cmd"
              fi
            else
              PNPM_PATH="$HOME/.local/share/pnpm/pnpm"
            fi
          fi

          if [ -f "$PNPM_PATH" ]; then
            echo "Found pnpm at: $PNPM_PATH"
            echo "PNPM_PATH=$PNPM_PATH" >> $GITHUB_ENV
            echo "$(dirname "$PNPM_PATH")" >> $GITHUB_PATH
          else
            echo "pnpm not found, installing globally with npm..."
            npm install -g pnpm@${{ inputs.pnpm-version }}
            if [ $? -ne 0 ]; then
              echo "Failed to install pnpm globally"
              exit 1
            fi
          fi

          # Verify pnpm is available
          pnpm --version || {
            echo "pnpm is still not available after setup"
            exit 1
          }

      - name: Verify package.json
        if: inputs.verify-package-json
        shell: bash
        working-directory: ${{ inputs.working-directory }}
        run: |
          if [ ! -f "package.json" ]; then
            if [ "${{ inputs.create-if-missing }}" = "true" ]; then
              echo "Creating minimal package.json..."
              echo '{
                "name": "paissive_income_test",
                "version": "1.0.0",
                "description": "Temporary package.json for testing",
                "private": true
              }' > package.json
            else
              echo "Error: package.json not found"
              exit 1
            fi
          fi

      - name: Install dependencies
        if: inputs.install-dependencies
        shell: bash
        working-directory: ${{ inputs.working-directory }}
        run: |
          pnpm install --reporter=default || {
            echo "Failed to install dependencies with pnpm"
            echo "Attempting to install with npm..."
            npm install || {
              echo "Failed to install dependencies with npm"
              exit 1
            }
          }

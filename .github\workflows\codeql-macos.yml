name: "CodeQL Analysis (macOS)"

# This workflow performs CodeQL analysis for JavaScript/TypeScript and Python on macOS
# It's a specialized version of the main CodeQL workflow for macOS-specific analysis

on:
  push:
    branches: [ main, dev, master, develop ]
    paths-ignore:
      - '**/*.md'
      - '**/*.txt'
      - '**/*.rst'
      - '**/*.png'
      - '**/*.jpg'
      - '**/*.jpeg'
      - '**/*.gif'
      - '**/*.svg'
      - '**/*.ico'
      - '.gitignore'
      - 'docs/**'
      - 'LICENSE'
      - 'SECURITY.md'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
  pull_request:
    branches: [ main, dev, master, develop ]
    paths-ignore:
      - '**/*.md'
      - '**/*.txt'
      - '**/*.rst'
      - '**/*.png'
      - '**/*.jpg'
      - '**/*.jpeg'
      - '**/*.gif'
      - '**/*.svg'
      - '**/*.ico'
      - '.gitignore'
      - 'docs/**'
      - 'LICENSE'
      - 'SECURITY.md'
      - 'CONTRIBUTING.md'
      - 'CODE_OF_CONDUCT.md'
  schedule:
    - cron: '0 4 * * 1'  # Weekly on Monday at 4 AM UTC (off-peak hours)
  workflow_dispatch:  # Allow manual triggering

# Limit concurrent runs to conserve resources
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  # Required for all workflows
  security-events: write
  # Only needed for workflows in private repositories
  actions: read
  contents: read

jobs:
  analyze:
    name: Analyze
    runs-on: macos-latest
    timeout-minutes: 90  # Increased timeout for larger codebases
    env:
      ACTIONS_RUNNER_DEBUG: true
      ACTIONS_STEP_DEBUG: true

    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript-typescript', 'python' ]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full git history for better analysis

      # Cache CodeQL database to speed up analysis
      - name: Cache CodeQL database
        uses: actions/cache@v4
        with:
          path: ~/.codeql/databases
          key: codeql-${{ matrix.language }}-macos-${{ github.sha }}
          restore-keys: |
            codeql-${{ matrix.language }}-macos-

      # Set up pnpm first
      - name: Setup pnpm
        if: matrix.language == 'javascript-typescript'
        uses: pnpm/action-setup@v4
        with:
          version: '8.15.4'
          run_install: false

      # Set up Node.js for JavaScript/TypeScript analysis
      - name: Set up Node.js
        if: matrix.language == 'javascript-typescript'
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          # Disable cache to avoid potential issues with pnpm
          # cache: 'pnpm'

      # Add pnpm to PATH
      - name: Add pnpm to PATH
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          # Add both global and local pnpm to PATH
          echo "PATH=$(pnpm -g bin):$(pnpm bin):$PATH" >> $GITHUB_ENV
          # Verify pnpm is in PATH
          echo "Current PATH: $PATH"
          which pnpm || {
            echo "pnpm not found in PATH. Installing globally with npm..."
            npm install -g pnpm
            echo "PATH=$(npm bin -g):$PATH" >> $GITHUB_ENV
            which pnpm || { echo "Error: pnpm is still not in PATH after npm install."; exit 1; }
          }

      # Set up Python for Python analysis
      - name: Set up Python
        if: matrix.language == 'python'
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'pip'

      # Verify lock files exist before analysis with enhanced error handling
      - name: Verify lock files
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          echo "Verifying lock files exist before CodeQL analysis..."
          LOCK_FILES=$(find . -name "package-lock.json" -o -name "yarn.lock" -o -name "pnpm-lock.yaml")

          if [ -z "$LOCK_FILES" ]; then
            echo "Warning: No lock files found. Creating a minimal package-lock.json in the root directory."

            # Create a more robust minimal valid package-lock.json with echo
            echo '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3,"requires":true,"packages":{"":{"name":"paissive-income","version":"1.0.0"}}}' > package-lock.json

            echo "Created minimal package-lock.json in the root directory."

            # Verify the file was created and has valid content
            if [ -f "package-lock.json" ]; then
              echo "Verification: package-lock.json exists and contains:"
              cat package-lock.json

              # Verify JSON is valid
              if command -v node &>/dev/null; then
                echo "Validating JSON with Node.js..."
                node -e "try { const data = require('./package-lock.json'); console.log('JSON is valid'); } catch(e) { console.error('Invalid JSON:', e.message); process.exit(1); }" || {
                  echo "Warning: package-lock.json contains invalid JSON. Creating a new one with simpler content..."
                  echo '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3}' > package-lock.json
                  echo "Created simplified package-lock.json."
                }
              else
                echo "Node.js not available for JSON validation, skipping validation step."
              fi

              # Add to .gitignore if it exists
              if [ -f ".gitignore" ]; then
                if ! grep -q "package-lock.json" .gitignore; then
                  echo "Adding package-lock.json to .gitignore..."
                  echo -e "\n# Generated during CodeQL analysis\npackage-lock.json" >> .gitignore
                fi
              fi
            else
              echo "CRITICAL ERROR: Failed to create package-lock.json, trying alternative method..."
              # Try an alternative method with printf
              printf '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3}\n' > package-lock.json

              if [ -f "package-lock.json" ]; then
                echo "Successfully created package-lock.json with alternative method."
                cat package-lock.json
              else
                echo "All methods to create package-lock.json failed. Creating an empty package.json instead..."
                echo '{"name":"paissive-income","version":"1.0.0"}' > package.json
                echo "Created package.json as fallback."
              fi
            fi
          else
            echo "Found lock files:"
            echo "$LOCK_FILES"
          fi
        continue-on-error: true

      # Install dependencies for JavaScript/TypeScript
      - name: Install Node.js dependencies
        if: matrix.language == 'javascript-typescript'
        shell: bash
        run: |
          # Function to install dependencies with fallback
          install_deps() {
            local dir=$1
            local original_dir=$(pwd)

            if [ -n "$dir" ]; then
              cd "$dir" || return 1
            fi

            echo "Installing dependencies in $(pwd)..."

            # Try with pnpm first (preferred)
            if command -v pnpm &>/dev/null; then
              echo "Using pnpm to install dependencies..."
              pnpm install || {
                echo "pnpm install failed, trying with npm..."
                npm install || {
                  echo "Both pnpm and npm installation failed in $(pwd)"
                  if [ -n "$dir" ]; then
                    cd "$original_dir"
                  fi
                  return 1
                }
              }
            else
              # Fallback to npm if pnpm is not available
              echo "pnpm not found, installing globally..."
              npm install -g pnpm
              echo "Using newly installed pnpm to install dependencies..."
              pnpm install || {
                echo "pnpm install failed, trying with npm..."
                npm install || {
                  echo "Both pnpm and npm installation failed in $(pwd)"
                  if [ -n "$dir" ]; then
                    cd "$original_dir"
                  fi
                  return 1
                }
              }
            fi

            if [ -n "$dir" ]; then
              cd "$original_dir"
            fi

            return 0
          }

          # Install dependencies in root directory if package.json exists
          if [ -f "package.json" ]; then
            echo "Found package.json in root directory"
            install_deps
          fi

          # Install dependencies in ui/react_frontend if package.json exists
          if [ -f "ui/react_frontend/package.json" ]; then
            echo "Found package.json in ui/react_frontend"
            install_deps "ui/react_frontend"
          fi

          # Install dependencies in sdk/javascript if package.json exists
          if [ -f "sdk/javascript/package.json" ]; then
            echo "Found package.json in sdk/javascript"
            install_deps "sdk/javascript"
          fi
        continue-on-error: true

      # Install dependencies for Python
      - name: Install Python dependencies
        if: matrix.language == 'python'
        shell: bash
        run: |
          python -m pip install --upgrade pip
          python -m pip install --upgrade "uv>=0.7.8"
          if [ -f requirements.txt ]; then
            echo "Installing pip dependencies from requirements.txt using 'uv pip'..."
            uv pip install -r requirements.txt || {
              echo "Failed to install with uv pip, falling back to regular pip..."
              python -m pip install --upgrade pip
              pip install -r requirements.txt
            }
          elif [ -f requirements-dev.txt ]; then
            echo "Installing pip dependencies from requirements-dev.txt using 'uv pip'..."
            uv pip install -r requirements-dev.txt || {
              echo "Failed to install with uv pip, falling back to regular pip..."
              python -m pip install --upgrade pip
              pip install -r requirements-dev.txt
            }
          else
            echo "No requirements.txt or requirements-dev.txt found, skipping dependency installation."
          fi

          # Install package in development mode if setup.py exists
          if [ -f setup.py ]; then
            echo "Installing package in development mode using 'uv pip'..."
            uv pip install -e . || {
              echo "Failed to install package with uv pip, falling back to regular pip..."
              python -m pip install --upgrade pip
              pip install -e .
            }
          fi
        continue-on-error: true

      # Ensure CodeQL configuration files exist
      - name: Ensure CodeQL configuration files
        shell: bash
        run: |
          if [ -f ".github/scripts/ensure-codeql-configs.sh" ]; then
            echo "Running ensure-codeql-configs.sh script..."
            bash .github/scripts/ensure-codeql-configs.sh
          else
            echo "ensure-codeql-configs.sh script not found. Creating minimal configuration..."

            # Ensure directory exists
            mkdir -p .github/codeql

                        # Create minimal macOS configuration
            echo 'name: "CodeQL Configuration for macOS"' > .github/codeql/security-os-macos.yml
            echo 'os: macos-latest' >> .github/codeql/security-os-macos.yml
            echo 'queries:' >> .github/codeql/security-os-macos.yml
            echo '  - uses: security-and-quality' >> .github/codeql/security-os-macos.yml
            echo '  - uses: security-extended' >> .github/codeql/security-os-macos.yml
            echo '  - uses: security' >> .github/codeql/security-os-macos.yml
            echo 'disable-default-queries: false' >> .github/codeql/security-os-macos.yml
            echo 'trap-for-errors: true' >> .github/codeql/security-os-macos.yml

            # Create minimal unified configuration
            echo 'name: "Unified CodeQL Configuration"' > .github/codeql/security-os-config.yml
            echo 'queries:' >> .github/codeql/security-os-config.yml
            echo '  - uses: security-and-quality' >> .github/codeql/security-os-config.yml
            echo '  - uses: security-extended' >> .github/codeql/security-os-config.yml
            echo '  - uses: security' >> .github/codeql/security-os-config.yml
            echo 'disable-default-queries: false' >> .github/codeql/security-os-config.yml
            echo 'trap-for-errors: true' >> .github/codeql/security-os-config.yml
          fi

      # Initialize CodeQL
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          queries: security-and-quality
          config-file: .github/codeql/security-os-macos.yml
          debug: true

      # Autobuild attempts to build any compiled languages
      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      # Display disk space before analysis
      - name: Check disk space
        shell: bash
        run: df -h

      # Perform CodeQL Analysis
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{ matrix.language }}"
          upload: true
          output: sarif-results/${{ matrix.language }}-macos.sarif

      # Upload SARIF results as an artifact
      - name: Upload SARIF results
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.language }}-macos-sarif
          path: sarif-results/${{ matrix.language }}-macos.sarif
          retention-days: 7

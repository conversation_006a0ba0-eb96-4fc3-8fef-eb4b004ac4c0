name: "CodeQL Windows"

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
  schedule:
    - cron: '30 1 * * 0'
  workflow_run:
    workflows: ["Gradual Lint Check"]
    types:
      - completed

jobs:
  analyze:
    name: Analyze
    runs-on: windows-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript-typescript', 'python' ]

    env:
      ACTIONS_RUNNER_DEBUG: true
      ACTIONS_STEP_DEBUG: true

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        if: matrix.language == 'javascript-typescript'
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup Python
        if: matrix.language == 'python'
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip'

      - name: Check disk space
        shell: pwsh
        run: Get-Volume

      # Create a simple package-lock.json if none exists - simplified approach
      - name: Create package-lock.json if needed
        if: matrix.language == 'javascript-typescript'
        shell: pwsh
        run: |
          Write-Host "Checking for lock files before CodeQL analysis..."
          $lockFiles = @(Get-ChildItem -Path . -Recurse -Include "package-lock.json","yarn.lock","pnpm-lock.yaml" -ErrorAction SilentlyContinue)

          if ($lockFiles.Count -gt 0) {
            Write-Host "Found $($lockFiles.Count) lock files:"
            $lockFiles | ForEach-Object {
              Write-Host "  $($_.FullName)"
            }
          } else {
            Write-Host "No lock files found. Creating a minimal package-lock.json..."

            # Define a simple valid JSON content
            $minimalJson = '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3,"requires":true,"packages":{"":{"name":"paissive-income","version":"1.0.0"}}}'

            # Create the file using multiple methods in sequence until one works
            $filePath = Join-Path -Path $PWD -ChildPath "package-lock.json"
            $fileCreated = $false

            # Try all methods in sequence
            try {
              Write-Host "Attempting to create package-lock.json using Set-Content..."
              Set-Content -Path $filePath -Value $minimalJson -Force
              $fileCreated = $true
              Write-Host "package-lock.json created successfully"
            } catch {
              Write-Host "Error creating package-lock.json with Set-Content: $_"

              try {
                Write-Host "Trying fallback with Out-File..."
                $minimalJson | Out-File -FilePath $filePath -Force
                $fileCreated = $true
                Write-Host "package-lock.json created with Out-File method"
              } catch {
                Write-Host "Error with Out-File method: $_"

                try {
                  Write-Host "Trying fallback with CMD..."
                  cmd /c "echo $minimalJson > package-lock.json"
                  $fileCreated = $true
                  Write-Host "package-lock.json created with CMD method"
                } catch {
                  Write-Host "All file creation methods failed: $_"
                }
              }
            }

            # Verify the file was created
            if (Test-Path $filePath) {
              $fileInfo = Get-Item $filePath
              Write-Host "package-lock.json exists: $($fileInfo.Length) bytes"

              # Add to .gitignore if it exists
              if (Test-Path ".gitignore") {
                $gitignore = Get-Content -Path ".gitignore" -Raw
                if (-not ($gitignore -match "package-lock\.json")) {
                  Write-Host "Adding package-lock.json to .gitignore..."
                  Add-Content -Path ".gitignore" -Value "`n# Generated during CodeQL analysis`npackage-lock.json" -Force
                }
              }
            } else {
              Write-Host "CRITICAL ERROR: Failed to create package-lock.json with any method"
            }
          }

      # Install dependencies for JavaScript/TypeScript
      - name: Install JS dependencies
        if: matrix.language == 'javascript-typescript'
        shell: pwsh
        run: |
          # Create node_modules directory if it doesn't exist
          if (-not (Test-Path "node_modules")) {
            Write-Host "Creating node_modules directory..."
            New-Item -ItemType Directory -Force -Path "node_modules" | Out-Null
          }

          # Add package-lock.json to .gitignore if it doesn't exist there already
          if (Test-Path ".gitignore") {
            $gitignore = Get-Content -Path ".gitignore" -Raw
            if (-not ($gitignore -match "package-lock\.json")) {
              Write-Host "Adding package-lock.json to .gitignore..."
              Add-Content -Path ".gitignore" -Value "`n# Generated during CodeQL analysis`npackage-lock.json"
            }
          }

      # Install dependencies for Python
      - name: Install Python dependencies
        if: matrix.language == 'python'
        shell: pwsh
        run: |
          python -m pip install --upgrade "uv>=0.7.8"
          uv pip install -r requirements.txt || python -m pip install -r requirements.txt
        continue-on-error: true

      # Display environment information for debugging
      - name: Display environment information
        shell: pwsh
        run: |
          Write-Host "Current directory: $(Get-Location)"
          Write-Host "Directory contents:"
          Get-ChildItem -Path . | Format-Table Name, LastWriteTime, Length

          Write-Host "Checking for package-lock.json:"
          if (Test-Path "package-lock.json") {
            $fileInfo = Get-Item "package-lock.json"
            Write-Host "package-lock.json exists: $($fileInfo.Length) bytes"
            Get-Content -Path "package-lock.json" -First 5
          } else {
            Write-Host "package-lock.json not found!"
          }

      # Ensure CodeQL configuration files exist
      - name: Ensure CodeQL configuration files
        shell: pwsh
        run: |
          if (Test-Path ".github/scripts/ensure-codeql-configs.ps1") {
            Write-Host "Running ensure-codeql-configs.ps1 script..."
            & .github/scripts/ensure-codeql-configs.ps1
          } else {
            Write-Host "ensure-codeql-configs.ps1 script not found. Creating minimal configuration..."

            # Ensure directory exists
            New-Item -ItemType Directory -Force -Path ".github/codeql" | Out-Null

            # Create minimal Windows configuration
            $windowsConfig = 'name: "CodeQL Configuration for Windows"' + "`n" +
                           'os: windows-latest' + "`n" +
                           'queries:' + "`n" +
                           '  - uses: security-and-quality' + "`n" +
                           '  - uses: security-extended' + "`n" +
                           '  - uses: security' + "`n" +
                           'disable-default-queries: false' + "`n" +
                           'trap-for-errors: true'
            Set-Content -Path ".github/codeql/security-os-windows.yml" -Value $windowsConfig -Encoding UTF8 -Force

            # Create minimal unified configuration
            $unifiedConfig = 'name: "Unified CodeQL Configuration"' + "`n" +
                           'queries:' + "`n" +
                           '  - uses: security-and-quality' + "`n" +
                           '  - uses: security-extended' + "`n" +
                           '  - uses: security' + "`n" +
                           'disable-default-queries: false' + "`n" +
                           'trap-for-errors: true'
            Set-Content -Path ".github/codeql/security-os-config.yml" -Value $unifiedConfig -Encoding UTF8 -Force
          }

      # Initialize CodeQL with Windows-specific configuration
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          queries: security-and-quality
          config-file: .github/codeql/security-os-windows.yml
          debug: true # Enable debug mode for more detailed logs

      # Verify CodeQL configuration file exists
      - name: Verify CodeQL configuration
        shell: pwsh
        run: |
          $configFile = ".github/codeql/security-os-windows.yml"
          if (Test-Path $configFile) {
            Write-Host "CodeQL configuration file exists: $configFile"
            Get-Content -Path $configFile -First 10
          } else {
            Write-Host "WARNING: CodeQL configuration file not found: $configFile"
            Write-Host "Creating minimal configuration file..."

            # Create configuration content
            $configContent = 'name: "CodeQL Configuration for Windows"' + "`n" +
                           '' + "`n" +
                           '# This configuration file customizes the CodeQL analysis for Windows' + "`n" +
                           '# It provides Windows-specific settings' + "`n" +
                           '' + "`n" +
                           '# Windows-specific settings' + "`n" +
                           'os: windows-latest' + "`n" +
                           '' + "`n" +
                           '# Query filters' + "`n" +
                           'queries:' + "`n" +
                           '  # Include the standard security and quality queries' + "`n" +
                           '  - uses: security-and-quality' + "`n" +
                           '' + "`n" +
                           '  # Include additional security queries' + "`n" +
                           '  - uses: security-extended' + "`n" +
                           '' + "`n" +
                           '  # Use standard security queries' + "`n" +
                           '  - uses: security' + "`n" +
                           '' + "`n" +
                           '# Disable noisy alerts' + "`n" +
                           'disable-default-queries: false' + "`n" +
                           '' + "`n" +
                           '# Trap errors during extraction' + "`n" +
                           'trap-for-errors: true'
            # Create the configuration file
            Set-Content -Path $configFile -Value $configContent -Encoding UTF8 -Force

            # Ensure directory exists
            New-Item -ItemType Directory -Force -Path ".github/codeql" | Out-Null

            # Create the configuration file
            Set-Content -Path $configFile -Value $configContent -Encoding UTF8 -Force

            Write-Host "Created minimal CodeQL configuration file: $configFile"
          }

          # Also check for the unified configuration file
          $unifiedConfigFile = ".github/codeql/security-os-config.yml"
          if (-not (Test-Path $unifiedConfigFile) -and (Test-Path $configFile)) {
            Write-Host "Unified configuration file not found. Creating a copy from the Windows configuration..."
            Copy-Item -Path $configFile -Destination $unifiedConfigFile -Force
            Write-Host "Created unified configuration file: $unifiedConfigFile"
          }

      # Autobuild attempts to build any compiled languages with enhanced error handling
      - name: Autobuild
        uses: github/codeql-action/autobuild@v3
        continue-on-error: true # Continue even if autobuild fails

      # Verify environment before CodeQL analysis - enhanced version
      - name: Verify environment
        shell: pwsh
        run: |
          Write-Host "Verifying environment before CodeQL analysis..."

          # Create sarif-results directory if it doesn't exist
          if (-not (Test-Path "sarif-results")) {
            Write-Host "Creating sarif-results directory..."
            New-Item -ItemType Directory -Force -Path "sarif-results" | Out-Null
          }

          # JavaScript/TypeScript specific checks
          if ("${{ matrix.language }}" -eq 'javascript-typescript') {
            # Final check for package-lock.json - create it if it still doesn't exist
            if (-not (Test-Path "package-lock.json")) {
              Write-Host "WARNING: package-lock.json still not found! Creating it as a last resort..."

              # Define the minimal valid JSON content
              $minimalJson = '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3}'

              # Try all methods in sequence until one works
              $methods = @(
                @{
                  Name = "System.IO.File";
                  Action = { [System.IO.File]::WriteAllText("$PWD\package-lock.json", $minimalJson) }
                },
                @{
                  Name = "Out-File";
                  Action = { $minimalJson | Out-File -FilePath "package-lock.json" -Encoding utf8 -Force }
                },
                @{
                  Name = "Set-Content";
                  Action = { Set-Content -Path "package-lock.json" -Value $minimalJson -Encoding UTF8 -Force }
                },
                @{
                  Name = "Echo Command";
                  Action = { cmd /c "echo {\"name\":\"paissive-income\",\"version\":\"1.0.0\",\"lockfileVersion\":3} > package-lock.json" }
                }
              )

              $success = $false
              foreach ($method in $methods) {
                if (-not $success) {
                  try {
                    Write-Host "Trying to create package-lock.json using $($method.Name)..."
                    & $method.Action

                    if (Test-Path "package-lock.json") {
                      $fileInfo = Get-Item "package-lock.json"
                      Write-Host "Successfully created package-lock.json using $($method.Name) ($($fileInfo.Length) bytes)"
                      $success = $true
                    }
                  } catch {
                    Write-Host "Error with $($method.Name) method: $_"
                  }
                }
              }

              if (-not $success) {
                Write-Host "CRITICAL ERROR: All methods to create package-lock.json failed!"
              }
            }

            # Verify package-lock.json
            if (Test-Path "package-lock.json") {
              $fileInfo = Get-Item "package-lock.json"
              Write-Host "package-lock.json exists: $($fileInfo.Length) bytes"

              # Read content safely
              try {
                $content = Get-Content -Path "package-lock.json" -Raw -ErrorAction Stop
                Write-Host "Content: $content"

                # Verify JSON is valid
                try {
                  $null = ConvertFrom-Json -InputObject $content -ErrorAction Stop
                  Write-Host "package-lock.json contains valid JSON"
                } catch {
                  Write-Host "WARNING: package-lock.json contains invalid JSON: $_"
                  # Fix it with a guaranteed valid JSON
                  $validJson = '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3}'
                  Set-Content -Path "package-lock.json" -Value $validJson -Encoding UTF8 -Force
                  Write-Host "Replaced with valid JSON"
                }
              } catch {
                Write-Host "Error reading package-lock.json: $_"
                # Create a new file with valid content
                $validJson = '{"name":"paissive-income","version":"1.0.0","lockfileVersion":3}'
                Set-Content -Path "package-lock.json" -Value $validJson -Encoding UTF8 -Force
                Write-Host "Created new package-lock.json with valid JSON"
              }
            } else {
              Write-Host "CRITICAL ERROR: package-lock.json still not found after multiple attempts!"
            }

            # Check for node_modules directory
            if (-not (Test-Path "node_modules")) {
              Write-Host "Creating node_modules directory..."
              New-Item -ItemType Directory -Force -Path "node_modules" | Out-Null
            }
          }

          # Python specific checks
          if ("${{ matrix.language }}" -eq 'python') {
            # Verify Python environment
            Write-Host "Verifying Python environment..."

            # Check Python version
            try {
              $pythonVersion = python --version
              Write-Host "Python version: $pythonVersion"
            } catch {
              Write-Host "Error checking Python version: $_"
            }

            # Check pip
            try {
              $pipVersion = pip --version
              Write-Host "Pip version: $pipVersion"
            } catch {
              Write-Host "Error checking pip version: $_"
            }
          }

          # Ensure .gitignore contains package-lock.json
          if (Test-Path ".gitignore") {
            $gitignore = Get-Content -Path ".gitignore" -Raw
            if (-not ($gitignore -match "package-lock\.json")) {
              Write-Host "Adding package-lock.json to .gitignore..."
              Add-Content -Path ".gitignore" -Value "`n# Generated during CodeQL analysis`npackage-lock.json" -Force
            }
          }

          # List all directories in the current path
          Write-Host "Current directory structure:"
          Get-ChildItem -Path . -Directory | ForEach-Object {
            Write-Host "  $($_.Name)"
          }

          Write-Host "Environment verification complete. Using standard CodeQL queries."

      # Perform CodeQL Analysis with enhanced error handling
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{ matrix.language }}"
          upload: true
          output: sarif-results/${{ matrix.language }}-windows.sarif
        continue-on-error: false # Fail the workflow if CodeQL analysis fails

      # Upload SARIF results as an artifact (with fallback for failures)
      - name: Upload SARIF results
        uses: actions/upload-artifact@v4
        if: always() # Always attempt to upload artifacts even if previous steps fail
        with:
          name: ${{ matrix.language }}-windows-sarif
          path: sarif-results/${{ matrix.language }}-windows.sarif
          retention-days: 7
          if-no-files-found: warn # Warn but don't fail if no files are found

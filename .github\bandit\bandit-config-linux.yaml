# Bandit Configuration for Linux (Run ID: generic)
# This configuration is used by GitHub Advanced Security for Bandit scanning on Linux

# Exclude directories from security scans
exclude_dirs:
  # Test directories
  - tests

  # Virtual environments
  - venv
  - .venv
  - env
  - .env

  # Cache directories
  - __pycache__
  - .pytest_cache
  - .mypy_cache
  - .ruff_cache

  # Custom stubs and dependencies
  - custom_stubs
  - node_modules

  # Build artifacts
  - build
  - dist
  - docs
  - docs_source
  - junit
  - bin
  - dev_tools
  - scripts
  - tool_templates

  # Documentation
  - docs
  - docs_source

  # Tools and utilities
  - junit
  - bin
  - dev_tools
  - scripts
  - tool_templates

# Skip specific test IDs
skips:
  # B101: Use of assert detected
  - B101
  # B311: Standard pseudo-random generators are not suitable for security/cryptographic purposes
  - B311

# Set the output format for GitHub Advanced Security
output_format: sarif

# Output file for GitHub Advanced Security
output_file: security-reports/bandit-results.sarif

# Set the severity level for GitHub Advanced Security
# Options: LOW, MEDIUM, HIGH
severity: MEDIUM

# Set the confidence level for GitHub Advanced Security
# Options: LOW, MEDIUM, HIGH
confidence: MEDIUM

# Shell injection configuration
# This section configures how Bandit detects potential shell injection vulnerabilities
# - no_shell: List of commands that don't use shell=True in subprocess calls
# - shell: List of commands that are allowed to use shell=True in subprocess calls
shell_injection:
  no_shell: []  # Commands that don't use shell=True
  shell: []     # Commands that are allowed to use shell=True

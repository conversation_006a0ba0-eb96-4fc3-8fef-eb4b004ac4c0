name: Setup uv (Reusable)

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
  workflow_run:
    workflows: ["Gradual Lint Check"]
    types:
      - completed
    branches:
      - main

jobs:
  setup-uv:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
    steps:
      - name: Set up Python ${{ inputs.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ inputs.python-version }}

      - name: Install uv using official action
        uses: astral-sh/setup-uv@v6
        with:
          version: "0.7.8"
          enable-cache: true

      - name: Verify uv installation
        run: |
          uv --version
          python --version

      - name: Install dependencies (if requested)
        if: inputs.install-dependencies == true
        working-directory: ${{ inputs.working-directory }}
        run: |
          if [ -f ${{ inputs.requirements-file }} ]; then
            uv pip install -r ${{ inputs.requirements-file }}
          fi
          if [ -f ${{ inputs.dev-requirements-file }} ]; then
            uv pip install -r ${{ inputs.dev-requirements-file }}
          fi
        shell: bash
